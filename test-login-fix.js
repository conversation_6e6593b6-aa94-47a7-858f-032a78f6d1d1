const axios = require('axios');

console.log('🔐 Testing login functionality...\n');

// Test with existing user
const testLogin = async () => {
  try {
    console.log('📡 Testing login API endpoint...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'password123' // Common test password
    };
    
    console.log('🔍 Attempting login with:', loginData.email);
    
    const response = await axios.post('http://localhost:5000/api/users/login', loginData, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login Response Status:', response.status);
    console.log('✅ Login Response:', response.data);
    
    if (response.data.success) {
      console.log('🎉 LOGIN SUCCESSFUL!');
      console.log('🔑 Token received:', response.data.data ? 'Yes' : 'No');
    } else {
      console.log('❌ Login failed:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Login test failed:');
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    } else if (error.request) {
      console.error('   No response received - server might not be running');
      console.error('   Make sure server is running on http://localhost:5000');
    } else {
      console.error('   Error:', error.message);
    }
  }
};

// Test server health first
const testHealth = async () => {
  try {
    console.log('🏥 Testing server health...');
    const response = await axios.get('http://localhost:5000/api/health', { timeout: 5000 });
    console.log('✅ Server is healthy:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed');
    console.error('   Make sure the server is running on port 5000');
    return false;
  }
};

// Run tests
(async () => {
  const serverHealthy = await testHealth();
  if (serverHealthy) {
    await testLogin();
  }
})();
