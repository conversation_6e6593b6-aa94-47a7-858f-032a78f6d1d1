const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

require('dotenv').config({ path: './server/.env' });

const app = express();
const port = 5000;

console.log('🚀 Starting BrainWave Server...');

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
app.use(express.json());

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 30000
}).then(() => {
  console.log('✅ MongoDB Connected Successfully');
}).catch(err => {
  console.error('❌ MongoDB Connection Failed:', err.message);
});

// User Model (simplified)
const User = require('./server/models/userModel');

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running', timestamp: new Date() });
});

// Login route
app.post('/api/users/login', async (req, res) => {
  try {
    console.log('🔐 Login attempt:', req.body.email);
    
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        message: 'Email and password are required',
        success: false
      });
    }

    // Find user by email or username
    const user = await User.findOne({
      $or: [
        { email: email },
        { username: email }
      ]
    });

    if (!user) {
      return res.status(200).json({
        message: 'User does not exist',
        success: false
      });
    }

    if (user.isBlocked) {
      return res.status(403).json({
        message: 'You are blocked. Please contact your moderator',
        success: false
      });
    }

    // Check password
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(200).json({
        message: 'Invalid password',
        success: false
      });
    }

    // Generate token
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    console.log('✅ Login successful for:', email);

    res.json({
      message: 'User logged in successfully',
      success: true,
      data: token,
      response: user
    });

  } catch (error) {
    console.error('❌ Login error:', error.message);
    res.status(500).json({
      message: error.message,
      success: false
    });
  }
});

// Start server
app.listen(port, () => {
  console.log(`✅ BrainWave Server running on http://localhost:${port}`);
  console.log(`🔗 Health check: http://localhost:${port}/api/health`);
  console.log(`🔐 Login endpoint: POST http://localhost:${port}/api/users/login`);
});
